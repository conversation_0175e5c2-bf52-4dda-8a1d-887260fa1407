import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:my_fincance_app/models/budget.dart';
import 'package:my_fincance_app/models/category.dart';
import 'package:my_fincance_app/models/loan.dart';
import 'package:my_fincance_app/models/transaction.dart';
import 'package:my_fincance_app/models/user.dart';
import 'package:my_fincance_app/pages/auth/login_page.dart';
import 'package:my_fincance_app/pages/home_page.dart';
import 'package:my_fincance_app/services/auth_service.dart';
import 'package:my_fincance_app/services/budget_service.dart';
import 'package:my_fincance_app/services/capital_service.dart';
import 'package:my_fincance_app/services/category_service.dart';
import 'package:my_fincance_app/services/transaction_service.dart';
import 'package:my_fincance_app/services/loan_service.dart';
import 'package:provider/provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await Hive.initFlutter();

  Hive.registerAdapter(UserAdapter());
  Hive.registerAdapter(LoanAdapter());
  Hive.registerAdapter(CategoryAdapter());
  Hive.registerAdapter(TransactionAdapter());
  Hive.registerAdapter(BudgetAdapter());

  await Hive.openBox<User>('users');
  await Hive.openBox<Loan>('loans');
  await Hive.openBox<Category>('categories');
  await Hive.openBox<Transaction>('transactions');
  await Hive.openBox<Budget>('budgets');
  await Hive.openBox('settings');

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => AuthService()),
        ChangeNotifierProxyProvider<AuthService, CategoryService>(
          create: (context) => CategoryService(''),
          update: (context, authService, categoryService) {
            final userId = authService.currentUserId;
            return CategoryService(userId);
          },
        ),
        ChangeNotifierProxyProvider<AuthService, LoanService>(
          create: (context) => LoanService(''),
          update: (context, authService, loanService) {
            final userId = authService.currentUserId;
            return LoanService(userId);
          },
        ),
        ChangeNotifierProxyProvider2<AuthService, LoanService, TransactionService>(
          create: (context) => TransactionService(''),
          update: (context, authService, loanService, transactionService) {
            final userId = authService.currentUserId;
            final newTransactionService = TransactionService(userId);
            newTransactionService.setLoanService(loanService);
            return newTransactionService;
          },
        ),
        ChangeNotifierProxyProvider<AuthService, BudgetService>(
          create: (context) => BudgetService(''),
          update: (context, authService, budgetService) {
            final userId = authService.currentUserId;
            return BudgetService(userId);
          },
        ),
        ChangeNotifierProxyProvider2<TransactionService, LoanService, CapitalService>(
          create: (context) => CapitalService(
            TransactionService(''),
            LoanService(''),
          ),
          update: (context, transactionService, loanService, capitalService) {
            return CapitalService(transactionService, loanService);
          },
        ),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'مرکز مالی من',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        textTheme: GoogleFonts.lalezarTextTheme(
          Theme.of(context).textTheme,
        ),
      ),
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('fa', ''), // Persian
      ],
      locale: const Locale('fa', ''),
      home: Consumer<AuthService>(
        builder: (context, authService, child) {
          // Auto-login for single user app
          if (authService.isFirstTime || !authService.isLoggedIn) {
            authService.autoLogin();
          }

          return authService.isLoggedIn ? const HomePage() : const LoginPage();
        },
      ),
    );
  }
}