import 'package:hive/hive.dart';
import 'package:my_fincance_app/main.dart';
import 'package:my_fincance_app/models/budget.dart';
import 'package:my_fincance_app/models/category.dart';
import 'package:my_fincance_app/models/loan.dart';
import 'package:my_fincance_app/models/transaction.dart';

class SyncService {
  final _categoryBox = Hive.box<Category>('categories');
  final _loanBox = Hive.box<Loan>('loans');
  final _transactionBox = Hive.box<Transaction>('transactions');
  final _budgetBox = Hive.box<Budget>('budgets');

  Future<void> initialSync() async {
    await _pushLocalChanges();
    await _pullCloudChanges();
  }

  // Pushes all local changes to the cloud
  Future<void> _pushLocalChanges() async {
    final userId = supabase.auth.currentUser!.id;

    // --- Push Categories ---
    await _pushItems<Category>(_categoryBox, 'categories', (c) => c.to<PERSON><PERSON>());

    // --- Push Loans ---
    await _pushItems<Loan>(_loanBox, 'loans', (l) => l.toJson());
    
    // --- Push Transactions (more complex due to foreign keys) ---
    final unsyncedTransactions = _transactionBox.values.where((t) => t.needsSync).toList();
    if (unsyncedTransactions.isNotEmpty) {
      final dataToUpsert = <Map<String, dynamic>>[];
      for (var t in unsyncedTransactions) {
        final category = _categoryBox.get(t.categoryId);
        final loan = t.loanId != null ? _loanBox.get(t.loanId) : null;
        if (category?.supabaseId != null) { // Ensure foreign keys are synced
           dataToUpsert.add(t.toJson(category!.supabaseId, loan?.supabaseId));
        }
      }
      final response = await supabase.from('transactions').upsert(dataToUpsert).select();
      await _updateLocalItemsWithSupabaseId<Transaction>(_transactionBox, response, (item) => item.description); // Using description as a simple matcher
    }

    // --- Push Budgets (also has foreign key) ---
     final unsyncedBudgets = _budgetBox.values.where((b) => b.needsSync).toList();
     if (unsyncedBudgets.isNotEmpty) {
       final dataToUpsert = <Map<String, dynamic>>[];
       for (var b in unsyncedBudgets) {
         final category = _categoryBox.get(b.categoryId);
         if (category?.supabaseId != null) {
           dataToUpsert.add(b.toJson(category.supabaseId));
         }
       }
       final response = await supabase.from('budgets').upsert(dataToUpsert).select();
       await _updateLocalItemsWithSupabaseId<Budget>(_budgetBox, response, (item) => '${item.categoryId}-${item.period.toIso8601String()}');
     }
  }

  // Pulls all changes from the cloud
  Future<void> _pullCloudChanges() async {
    final userId = supabase.auth.currentUser!.id;
    final lastSyncTime = '1970-01-01T00:00:00Z'; // TODO: Implement proper last sync timestamp

    // --- Pull Categories ---
    final catResponse = await supabase.from('categories').select().eq('user_id', userId).gt('last_modified', lastSyncTime);
    await _syncCloudToLocal<Category>(_categoryBox, catResponse, (json) {
      final localId = _findLocalIdBySupabaseId(_categoryBox, json['id']) ?? Uuid().v4();
      return Category.fromJson(json, localId);
    });
    
    // --- Pull Loans ---
    final loanResponse = await supabase.from('loans').select().eq('user_id', userId).gt('last_modified', lastSyncTime);
    await _syncCloudToLocal<Loan>(_loanBox, loanResponse, (json) {
      final localId = _findLocalIdBySupabaseId(_loanBox, json['id']) ?? Uuid().v4();
      return Loan.fromJson(json, localId);
    });

    // --- Pull Transactions ---
    final transResponse = await supabase.from('transactions').select().eq('user_id', userId).gt('last_modified', lastSyncTime);
    await _syncCloudToLocal<Transaction>(_transactionBox, transResponse, (json) {
        final localCatId = _findLocalIdBySupabaseId(_categoryBox, json['category_id']);
        final localLoanId = json['loan_id'] != null ? _findLocalIdBySupabaseId(_loanBox, json['loan_id']) : null;
        if(localCatId == null) return null; // Cannot add transaction without category

        final localId = _findLocalIdBySupabaseId(_transactionBox, json['id']) ?? Uuid().v4();
        return Transaction.fromJson(json, localId, localCatId, localLoanId);
    });
    
    // --- Pull Budgets ---
    final budgetResponse = await supabase.from('budgets').select().eq('user_id', userId).gt('last_modified', lastSyncTime);
    await _syncCloudToLocal<Budget>(_budgetBox, budgetResponse, (json) {
        final localCatId = _findLocalIdBySupabaseId(_categoryBox, json['category_id']);
        if(localCatId == null) return null;

        final localId = _findLocalIdBySupabaseId(_budgetBox, json['id']) ?? Uuid().v4();
        return Budget.fromJson(json, localId, localCatId);
    });
  }

  // --- Generic Helper Methods ---

  Future<void> _pushItems<T extends HiveObject>(Box<T> box, String tableName, Map<String, dynamic> Function(T) toJson) async {
    final unsyncedItems = box.values.where((item) => (item as dynamic).needsSync).toList();
    if (unsyncedItems.isNotEmpty) {
      final dataToUpsert = unsyncedItems.map((item) => toJson(item)).toList();
      final response = await supabase.from(tableName).upsert(dataToUpsert).select();
      await _updateLocalItemsWithSupabaseId<T>(box, response, (item) => (item as dynamic).id);
    }
  }
  
  Future<void> _updateLocalItemsWithSupabaseId<T extends HiveObject>(Box<T> box, List<dynamic> response, dynamic Function(T) matcher) async {
      for (var syncedData in response) {
          final localItem = box.values.firstWhere((item) => matcher(item) == matcher(T.fromJson(syncedData, 'temp_id')), orElse: () => null);
          if (localItem != null) {
              (localItem as dynamic).supabaseId = syncedData['id'];
              (localItem as dynamic).needsSync = false;
              await localItem.save();
          }
      }
  }

  Future<void> _syncCloudToLocal<T extends HiveObject>(Box<T> box, List<dynamic> cloudItems, T? Function(Map<String, dynamic>) fromJson) async {
    for (var cloudJson in cloudItems) {
      final cloudSupabaseId = cloudJson['id'];
      final T? localItem = box.values.firstWhere((item) => (item as dynamic).supabaseId == cloudSupabaseId, orElse: () => null);
      
      if (localItem == null) { // New item from cloud
        final newItem = fromJson(cloudJson);
        if (newItem != null) {
          await box.put((newItem as dynamic).id, newItem);
        }
      } else { // Existing item, check for updates
        final cloudModified = DateTime.parse(cloudJson['last_modified']);
        if (cloudModified.isAfter((localItem as dynamic).lastModified)) {
           final updatedItem = fromJson(cloudJson);
            if (updatedItem != null) {
              await box.put((updatedItem as dynamic).id, updatedItem);
            }
        }
      }
    }
  }

  String? _findLocalIdBySupabaseId<T extends HiveObject>(Box<T> box, int supabaseId) {
      final T? item = box.values.firstWhere((item) => (item as dynamic).supabaseId == supabaseId, orElse: () => null);
      return (item as dynamic)?.id;
  }
}
