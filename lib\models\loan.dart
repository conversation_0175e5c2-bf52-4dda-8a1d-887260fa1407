import 'package:hive/hive.dart';

part 'loan.g.dart';

@HiveType(typeId: 1)
class Loan extends HiveObject {
  @HiveField(0)
  late String id;

  @HiveField(1)
  late String name;

  @HiveField(2)
  late String type; // "بدهی" or "طلب"

  @HiveField(3)
  late String person;

  @HiveField(4)
  late double initialAmount;

  @HiveField(5)
  late DateTime startDate;

  @HiveField(6)
  late String status; // "فعال" or "تمام شده"

  @HiveField(7)
  late String userId;
}
