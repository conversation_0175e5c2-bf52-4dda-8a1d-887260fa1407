import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:my_fincance_app/models/transaction.dart';
import 'package:my_fincance_app/services/loan_service.dart';
import 'package:uuid/uuid.dart';

class TransactionService extends ChangeNotifier {
  final Box<Transaction> _transactionBox = Hive.box<Transaction>('transactions');
  final String _userId;
  final _uuid = Uuid();
  LoanService? _loanService;

  List<Transaction> _transactions = [];

  TransactionService(this._userId) {
    _loadTransactions();
  }

  void setLoanService(LoanService loanService) {
    _loanService = loanService;
  }

  List<Transaction> get transactions => _transactions;

  /// Get transactions for current month
  List<Transaction> get currentMonthTransactions {
    final now = DateTime.now();
    return _transactions.where((t) =>
      t.date.year == now.year && t.date.month == now.month
    ).toList();
  }

  /// Get income transactions for current month
  List<Transaction> get currentMonthIncome {
    return currentMonthTransactions.where((t) => t.type == 'درآمد').toList();
  }

  /// Get expense transactions for current month
  List<Transaction> get currentMonthExpenses {
    return currentMonthTransactions.where((t) => t.type == 'مصرف').toList();
  }

  /// Get total income for current month
  double get totalCurrentMonthIncome {
    return currentMonthIncome.fold<double>(0.0, (sum, t) => sum + t.amount);
  }

  /// Get total expenses for current month
  double get totalCurrentMonthExpenses {
    return currentMonthExpenses.fold<double>(0.0, (sum, t) => sum + t.amount);
  }

  /// Get net savings for current month
  double get currentMonthNetSavings {
    return totalCurrentMonthIncome - totalCurrentMonthExpenses;
  }

  /// Get transactions for a specific loan
  List<Transaction> getTransactionsForLoan(String loanId) {
    return _transactions.where((t) => t.loanId == loanId).toList();
  }

  void _loadTransactions() {
    _transactions = _transactionBox.values.where((t) => t.userId == _userId).toList();
    notifyListeners();
  }

  void addTransaction(String description, double amount, String type, String categoryId, {String? loanId, DateTime? date}) {
    final newTransaction = Transaction()
      ..id = _uuid.v4()
      ..date = date ?? DateTime.now()
      ..description = description
      ..amount = amount
      ..type = type
      ..categoryId = categoryId
      ..loanId = loanId
      ..userId = _userId;
    _transactionBox.put(newTransaction.id, newTransaction);
    _loadTransactions();

    // Update loan statuses if this transaction is linked to a loan
    if (loanId != null && _loanService != null) {
      _loanService!.updateLoanStatuses();
    }
  }

  void updateTransaction(
    Transaction transaction,
    String description,
    double amount,
    String type,
    String categoryId, {
    String? loanId,
    DateTime? date,
  }) {
    final oldLoanId = transaction.loanId;

    transaction.description = description;
    transaction.amount = amount;
    transaction.type = type;
    transaction.categoryId = categoryId;
    transaction.loanId = loanId;
    transaction.date = date ?? transaction.date;
    transaction.save();

    _loadTransactions();

    // Update loan statuses if this transaction was linked to a loan
    if ((oldLoanId != null || loanId != null) && _loanService != null) {
      _loanService!.updateLoanStatuses();
    }
  }

  void deleteTransaction(Transaction transaction) {
    final loanId = transaction.loanId;
    transaction.delete();
    _loadTransactions();

    // Update loan statuses if this transaction was linked to a loan
    if (loanId != null && _loanService != null) {
      _loanService!.updateLoanStatuses();
    }
  }

  /// Get transactions by category for current month
  Map<String, double> getCurrentMonthExpensesByCategory() {
    final expenses = currentMonthExpenses;
    final Map<String, double> categoryTotals = {};

    for (final transaction in expenses) {
      categoryTotals[transaction.categoryId] =
          (categoryTotals[transaction.categoryId] ?? 0.0) + transaction.amount;
    }

    return categoryTotals;
  }

  /// Get total capital (all time income - all time expenses)
  double get totalCapital {
    final totalIncome = _transactions
        .where((t) => t.type == 'درآمد')
        .fold<double>(0.0, (sum, t) => sum + t.amount);

    final totalExpenses = _transactions
        .where((t) => t.type == 'مصرف')
        .fold<double>(0.0, (sum, t) => sum + t.amount);

    return totalIncome - totalExpenses;
  }

  /// Get total income (all time)
  double get totalIncome {
    return _transactions
        .where((t) => t.type == 'درآمد')
        .fold<double>(0.0, (sum, t) => sum + t.amount);
  }

  /// Get total expenses (all time)
  double get totalExpenses {
    return _transactions
        .where((t) => t.type == 'مصرف')
        .fold<double>(0.0, (sum, t) => sum + t.amount);
  }

  /// Get salary transactions (assuming salary is in a specific category)
  List<Transaction> get salaryTransactions {
    return _transactions.where((t) =>
      t.type == 'درآمد' &&
      t.description.toLowerCase().contains('معاش') ||
      t.description.toLowerCase().contains('حقوق') ||
      t.description.toLowerCase().contains('salary')
    ).toList();
  }

  /// Get total salary received
  double get totalSalary {
    return salaryTransactions.fold<double>(0.0, (sum, t) => sum + t.amount);
  }
}
