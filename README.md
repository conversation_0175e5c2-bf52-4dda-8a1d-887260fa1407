I want to build a powerful personal finance web application called "My Finance Hub" (مرکز مالی من). This is a single-user application. The entire User Interface must be in Persian (Farsi) and the default currency must be Afghani (AFN).

Core Idea:
The app allows a user to meticulously track all income and expenses, set monthly budgets, and most importantly, manage multiple debts (loans taken) and credits (loans given out). The app should provide a clear overview of the user's financial health, including their loan balances.

1. Data Models / Database Schema:

Please create the following data models. All data must be linked to the logged-in user.

User: (Critical addition for security and data ownership)

email: Text (primary identifier)

password: Hashed

name: Text

Loans: (Central table for debts and credits)

id: Unique Identifier

name: Text (e.g., "وام از مادر", "قرض به علی", "وام موتر")

type: Dropdown/Enum ("بدهی", "طلب")

person: Text (Name of the person or institution)

initial_amount: Number (AFN)

start_date: Date

status: Dropdown/Enum ("فعال", "تمام شده")

user_id: Foreign key to User table

Categories:

id: Unique Identifier

name: Text (e.g., "معاش", "غذا", "پرداخت قسط وام", "دریافت قسط طلب")

type: Dropdown/Enum ("درآمد", "مصرف")

user_id: Foreign key to User table

Transactions:

id: Unique Identifier

date: Date

description: Text

amount: Number (AFN)

type: Dropdown/Enum ("درآمد", "مصرف")

category_id: Foreign key to Categories table

loan_id: (Optional) Foreign key to Loans table. This links a transaction to a specific loan.

user_id: Foreign key to User table

Budgets:

id: Unique Identifier

period: Date (The system will use the month and year of this date)

amount: Number (AFN)

category_id: Foreign key to an expense Category.

user_id: Foreign key to User table

2. Pages and UI Components (All labels in Persian):

Page 1: داشبورد (Dashboard)

Summary Cards (for the current month): "مجموع درآمد", "مجموع مصارف", "پس‌انداز خالص" (Income - Expenses).

Loan Summary Cards:

مجموع بدهی‌های فعال: Displays the total remaining balance of all active "بدهی" type loans.

مجموع طلب‌های فعال: Displays the total remaining balance of all active "طلب" type loans.

Charts:

A pie chart for expense breakdown by category.

A bar chart comparing budgeted vs. actual spending for each category with a budget.

Page 2: مدیریت وام‌ها (Loan Management)

This page must have two tabs: "بدهی‌های من" and "طلب‌های من".

Each tab shows a list of the corresponding loans. For each loan, display: نام وام, شخص, مبلغ کل, مبلغ باقیمانده, and a progress bar showing repayment progress.

A prominent button labeled "ثبت وام جدید" opens a form to add a new record to the Loans table.

Loan Detail View: Clicking on any loan in the list navigates to a detailed view for that loan. This view shows all its information and a filtered list of all transactions linked to it (using loan_id).

Page 3: تراکنش‌ها (Transactions)

A table of all transactions with filters (by date, category, type) and an "افزودن تراکنش جدید" button.

New Transaction Form (Crucial Update):

Fields: date, description, amount, category, type.

New Field: An optional dropdown labeled "وام مربوطه". This dropdown should be populated with the name of all loans with status: "فعال". The user selects a loan only if the transaction is a payment/receipt for that specific loan.

Page 4: بودجه‌بندی (Budgets)

A page to create, view, and update monthly budgets for expense categories. Show progress bars for each budget item.

Page 5: تنظیمات (Settings)

A simple page where the user can manage their categories (add, edit, delete).

3. Core Logic Summary:

The remaining balance for any loan is calculated as: Loans.initial_amount - SUM(Transactions.amount WHERE Transactions.loan_id matches that loan's id).

When the remaining balance of a loan becomes zero or less, its status should automatically change from "فعال" to "تمام شده".

The dashboard cards and charts should update in real-time as new transactions are added.

The entire system is dynamic. The user can define any number of loans, categories, and budgets.

4. UI/UX Design and Principles:

Language & Currency: The entire UI is in Persian. All numerical inputs and displays are for the Afghani (AFN) currency.

Design: Use a clean, data-focused, and modern design. Ensure good readability with a professional Persian font.

Responsiveness: The application must be fully responsive and usable on both desktop and mobile devices.