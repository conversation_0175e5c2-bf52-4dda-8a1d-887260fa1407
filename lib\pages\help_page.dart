import 'package:flutter/material.dart';

class HelpPage extends StatefulWidget {
  const HelpPage({super.key});

  @override
  State<HelpPage> createState() => _HelpPageState();
}

class _HelpPageState extends State<HelpPage> with TickerProviderStateMixin {
  int _selectedIndex = 0;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final List<HelpSection> _helpSections = [
    HelpSection(
      title: 'شروع کار',
      icon: Icons.play_arrow,
      items: [
        HelpItem(
          title: 'ثبت نام و ورود',
          content: 'برای شروع کار با برنامه، ابتدا باید حساب کاربری ایجاد کنید. پس از ثبت نام، می‌توانید با ایمیل و رمز عبور خود وارد شوید.',
        ),
        HelpItem(
          title: 'تنظیم دسته‌بندی‌ها',
          content: 'برنامه به طور خودکار دسته‌بندی‌های پایه را ایجاد می‌کند. می‌توانید در بخش تنظیمات، دسته‌بندی‌های جدید اضافه کنید یا موجودی‌ها را ویرایش کنید.',
        ),
        HelpItem(
          title: 'آشنایی با رابط کاربری',
          content: 'برنامه دارای ۵ بخش اصلی است: داشبورد، وام‌ها، تراکنش‌ها، بودجه‌بندی و تنظیمات. هر بخش برای مدیریت جنبه خاصی از امور مالی شما طراحی شده است.',
        ),
      ],
    ),
    HelpSection(
      title: 'مدیریت تراکنش‌ها',
      icon: Icons.receipt_long,
      items: [
        HelpItem(
          title: 'افزودن تراکنش جدید',
          content: 'برای افزودن تراکنش، روی دکمه "+" در صفحه تراکنش‌ها کلیک کنید. توضیحات، مبلغ، نوع (درآمد/مصرف) و دسته‌بندی را وارد کنید.',
        ),
        HelpItem(
          title: 'ویرایش و حذف تراکنش',
          content: 'برای ویرایش یا حذف تراکنش، روی منوی سه نقطه در کنار هر تراکنش کلیک کنید و گزینه مورد نظر را انتخاب کنید.',
        ),
        HelpItem(
          title: 'فیلتر کردن تراکنش‌ها',
          content: 'می‌توانید تراکنش‌ها را بر اساس نوع، دسته‌بندی و بازه زمانی فیلتر کنید. از آیکون فیلتر در بالای صفحه استفاده کنید.',
        ),
        HelpItem(
          title: 'ربط دادن به وام',
          content: 'هنگام افزودن تراکنش، می‌توانید آن را به وام خاصی ربط دهید. این کار به شما کمک می‌کند تا پیشرفت پرداخت وام‌ها را دنبال کنید.',
        ),
      ],
    ),
    HelpSection(
      title: 'مدیریت وام‌ها',
      icon: Icons.account_balance_wallet,
      items: [
        HelpItem(
          title: 'انواع وام',
          content: 'دو نوع وام وجود دارد: بدهی (پولی که شما بدهکار هستید) و طلب (پولی که به شما بدهکار هستند).',
        ),
        HelpItem(
          title: 'افزودن وام جدید',
          content: 'برای افزودن وام، نام وام، نوع، نام شخص/موسسه، مبلغ کل و تاریخ شروع را وارد کنید.',
        ),
        HelpItem(
          title: 'پیگیری پیشرفت وام',
          content: 'برنامه به طور خودکار پیشرفت پرداخت وام‌ها را محاسبه می‌کند. وقتی وامی کاملاً پرداخت شود، وضعیت آن به "تمام شده" تغییر می‌کند.',
        ),
        HelpItem(
          title: 'مشاهده جزئیات وام',
          content: 'با کلیک روی هر وام، می‌توانید جزئیات کامل آن شامل تاریخچه پرداخت‌ها را مشاهده کنید.',
        ),
      ],
    ),
    HelpSection(
      title: 'بودجه‌بندی',
      icon: Icons.pie_chart,
      items: [
        HelpItem(
          title: 'تعریف بودجه ماهانه',
          content: 'برای هر دسته‌بندی مصرف، می‌توانید بودجه ماهانه تعریف کنید. این کار به شما کمک می‌کند مصارف خود را کنترل کنید.',
        ),
        HelpItem(
          title: 'پیگیری بودجه',
          content: 'برنامه مصرف واقعی شما را با بودجه تعریف شده مقایسه می‌کند و در صورت تجاوز از بودجه، هشدار می‌دهد.',
        ),
        HelpItem(
          title: 'نمودار بودجه',
          content: 'در داشبورد می‌توانید نمودار مقایسه بودجه و مصرف واقعی را مشاهده کنید.',
        ),
      ],
    ),
    HelpSection(
      title: 'داشبورد و گزارش‌ها',
      icon: Icons.dashboard,
      items: [
        HelpItem(
          title: 'خلاصه مالی',
          content: 'در تب خلاصه مالی، اطلاعات کلی درآمد، مصرف، پس‌انداز و وضعیت وام‌های شما نمایش داده می‌شود.',
        ),
        HelpItem(
          title: 'گزارش ماهانه',
          content: 'تب گزارش ماهانه شامل نمودارهای تفصیلی مصارف و مقایسه بودجه است.',
        ),
        HelpItem(
          title: 'سرمایه کل',
          content: 'در تب سرمایه کل، ارزش خالص دارایی شما (درآمدها منهای مصارف و بدهی‌ها) نمایش داده می‌شود.',
        ),
      ],
    ),
    HelpSection(
      title: 'تنظیمات',
      icon: Icons.settings,
      items: [
        HelpItem(
          title: 'مدیریت پروفایل',
          content: 'می‌توانید نام خود را ویرایش کنید، رمز عبور را تغییر دهید یا از حساب خود خارج شوید.',
        ),
        HelpItem(
          title: 'مدیریت دسته‌بندی‌ها',
          content: 'دسته‌بندی‌های جدید اضافه کنید، موجودی‌ها را ویرایش کنید یا حذف کنید.',
        ),
      ],
    ),
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  List<HelpSection> get _filteredSections {
    if (_searchQuery.isEmpty) return _helpSections;

    return _helpSections.map((section) {
      final filteredItems = section.items.where((item) =>
        item.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
        item.content.toLowerCase().contains(_searchQuery.toLowerCase())
      ).toList();

      if (filteredItems.isNotEmpty) {
        return HelpSection(
          title: section.title,
          icon: section.icon,
          items: filteredItems,
        );
      }
      return null;
    }).where((section) => section != null).cast<HelpSection>().toList();
  }

  @override
  Widget build(BuildContext context) {
    final isTablet = MediaQuery.of(context).size.width > 768;

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'راهنمای کاربر',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
        backgroundColor: Colors.blue.shade50,
        elevation: 0,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(80),
          child: Container(
            padding: const EdgeInsets.all(16),
            child: _buildSearchBar(),
          ),
        ),
      ),
      backgroundColor: Colors.grey.shade50,
      body: isTablet ? _buildTabletLayout() : _buildMobileLayout(),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'جستجو در راهنما...',
          prefixIcon: const Icon(Icons.search, color: Colors.grey),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    setState(() {
                      _searchQuery = '';
                    });
                  },
                )
              : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
      ),
    );
  }

  Widget _buildTabletLayout() {
    return Row(
      children: [
        // Sidebar
        Container(
          width: 280,
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(2, 0),
              ),
            ],
          ),
          child: _buildSidebar(),
        ),

        // Content
        Expanded(
          child: _buildContent(),
        ),
      ],
    );
  }

  Widget _buildMobileLayout() {
    return _searchQuery.isNotEmpty
        ? _buildSearchResults()
        : PageView.builder(
            itemCount: _helpSections.length,
            onPageChanged: (index) {
              setState(() {
                _selectedIndex = index;
              });
            },
            itemBuilder: (context, index) {
              return _buildContent();
            },
          );
  }

  Widget _buildSidebar() {
    final filteredSections = _filteredSections;

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: filteredSections.length,
      itemBuilder: (context, index) {
        final section = filteredSections[index];
        final originalIndex = _helpSections.indexOf(section);
        final isSelected = originalIndex == _selectedIndex;

        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          decoration: BoxDecoration(
            gradient: isSelected
                ? LinearGradient(
                    colors: [Colors.blue.withValues(alpha: 0.1), Colors.blue.withValues(alpha: 0.05)],
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                  )
                : null,
            borderRadius: BorderRadius.circular(12),
            border: isSelected
                ? Border.all(color: Colors.blue.withValues(alpha: 0.3), width: 1)
                : null,
          ),
          child: ListTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: isSelected ? Colors.blue : Colors.grey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                section.icon,
                color: isSelected ? Colors.white : Colors.grey.shade600,
                size: 20,
              ),
            ),
            title: Text(
              section.title,
              style: TextStyle(
                fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                color: isSelected ? Colors.blue : Colors.black87,
                fontSize: 14,
              ),
            ),
            onTap: () {
              setState(() {
                _selectedIndex = originalIndex;
              });
              _animationController.reset();
              _animationController.forward();
            },
          ),
        );
      },
    );
  }

  Widget _buildContent() {
    final section = _helpSections[_selectedIndex];

    return FadeTransition(
      opacity: _fadeAnimation,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with breadcrumb
            _buildSectionHeader(section),
            const SizedBox(height: 32),

            // Help items
            ...section.items.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              return AnimatedContainer(
                duration: Duration(milliseconds: 200 + (index * 100)),
                child: _buildHelpItem(item, index),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchResults() {
    final filteredSections = _filteredSections;

    if (filteredSections.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'نتیجه‌ای یافت نشد',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'کلمات کلیدی دیگری را امتحان کنید',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: filteredSections.length,
      itemBuilder: (context, sectionIndex) {
        final section = filteredSections[sectionIndex];
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue.withValues(alpha: 0.2)),
              ),
              child: Row(
                children: [
                  Icon(section.icon, color: Colors.blue),
                  const SizedBox(width: 12),
                  Text(
                    section.title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                ],
              ),
            ),
            ...section.items.map((item) => _buildHelpItem(item, 0)),
            const SizedBox(height: 24),
          ],
        );
      },
    );
  }
  Widget _buildSectionHeader(HelpSection section) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue.withValues(alpha: 0.1), Colors.blue.withValues(alpha: 0.05)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.blue.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Icon(
              section.icon,
              size: 28,
              color: Colors.white,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  section.title,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${section.items.length} مورد راهنما',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHelpItem(HelpItem item, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: ExpansionTile(
          tilePadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
          childrenPadding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
          leading: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              '${index + 1}',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
          ),
          title: Text(
            item.title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                item.content,
                style: const TextStyle(
                  fontSize: 15,
                  height: 1.6,
                  color: Colors.black87,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class HelpSection {
  final String title;
  final IconData icon;
  final List<HelpItem> items;

  HelpSection({
    required this.title,
    required this.icon,
    required this.items,
  });
}

class HelpItem {
  final String title;
  final String content;

  HelpItem({
    required this.title,
    required this.content,
  });
}
