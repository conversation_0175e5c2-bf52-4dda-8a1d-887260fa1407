import 'package:flutter/material.dart';
import 'package:my_fincance_app/models/loan.dart';
import 'package:my_fincance_app/services/loan_service.dart';
import 'package:my_fincance_app/utils/currency_formatter.dart';
import 'package:my_fincance_app/utils/date_formatter.dart';
import 'package:my_fincance_app/utils/shamsi_date_picker.dart';
import 'package:my_fincance_app/pages/loan_detail_page.dart';
import 'package:provider/provider.dart';

class LoansPage extends StatefulWidget {
  const LoansPage({super.key});

  @override
  _LoansPageState createState() => _LoansPageState();
}

class _LoansPageState extends State<LoansPage> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مدیریت وام‌ها'),
        centerTitle: true,
        backgroundColor: Colors.blue.shade50,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.blue,
          unselectedLabelColor: Colors.grey,
          indicatorColor: Colors.blue,
          tabs: const [
            Tab(
              icon: Icon(Icons.money_off),
              text: 'بدهی‌های من',
            ),
            Tab(
              icon: Icon(Icons.account_balance_wallet),
              text: 'طلب‌های من',
            ),
          ],
        ),
      ),
      backgroundColor: Colors.grey.shade50,
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildLoanList(context, 'بدهی'),
          _buildLoanList(context, 'طلب'),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          _showLoanDialog(context, Provider.of<LoanService>(context, listen: false));
        },
        icon: const Icon(Icons.add),
        label: const Text('ثبت وام جدید'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  Widget _buildLoanList(BuildContext context, String type) {
    return Consumer<LoanService>(
      builder: (context, loanService, child) {
        final loans = type == 'بدهی' ? loanService.debtLoans : loanService.creditLoans;

        if (loans.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  type == 'بدهی' ? Icons.money_off : Icons.account_balance_wallet,
                  size: 80,
                  color: Colors.grey,
                ),
                const SizedBox(height: 16),
                Text(
                  'هیچ $typeی ثبت نشده است',
                  style: const TextStyle(
                    fontSize: 18,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'برای شروع، $type جدید اضافه کنید',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          );
        }

        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: ListView.builder(
            itemCount: loans.length,
            itemBuilder: (context, index) {
              final loan = loans[index];
              return _buildLoanCard(context, loan, loanService);
            },
          ),
        );
      },
    );
  }

  Widget _buildLoanCard(BuildContext context, Loan loan, LoanService loanService) {
    final remainingBalance = loanService.getRemainingBalance(loan.id);
    final progress = loanService.getLoanProgress(loan.id);
    final isCompleted = loan.status == 'تمام شده';

    return Card(
      elevation: 4,
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => LoanDetailPage(loanId: loan.id),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          loan.name,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(
                              Icons.person,
                              size: 16,
                              color: Colors.grey.shade600,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              loan.person,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: isCompleted ? Colors.green : Colors.orange,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      loan.status,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      if (value == 'edit') {
                        _showLoanDialog(context, loanService, loan: loan);
                      } else if (value == 'delete') {
                        _showDeleteLoanDialog(context, loan);
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, color: Colors.blue),
                            SizedBox(width: 8),
                            Text('ویرایش'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, color: Colors.red),
                            SizedBox(width: 8),
                            Text('حذف'),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Amount information
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'مبلغ کل',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        Text(
                          CurrencyFormatter.formatWithPersianDigits(loan.initialAmount),
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'مبلغ باقیمانده',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        Text(
                          CurrencyFormatter.formatWithPersianDigits(remainingBalance),
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: remainingBalance > 0 ? Colors.red : Colors.green,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Progress bar
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'پیشرفت پرداخت',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                      Text(
                        '${(progress * 100).toStringAsFixed(1)}%',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  LinearProgressIndicator(
                    value: progress,
                    backgroundColor: Colors.grey.shade300,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      isCompleted ? Colors.green : Colors.blue,
                    ),
                    minHeight: 8,
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // Date and action row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'تاریخ شروع: ${DateFormatter.formatPersianDate(loan.startDate)}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: Colors.grey.shade400,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showLoanDialog(BuildContext context, LoanService loanService, {Loan? loan}) {
    final formKey = GlobalKey<FormState>();
    final nameController = TextEditingController(text: loan?.name ?? '');
    final personController = TextEditingController(text: loan?.person ?? '');
    final amountController = TextEditingController(text: loan?.initialAmount.toString() ?? '');
    String type = loan?.type ?? 'بدهی';
    DateTime startDate = loan?.startDate ?? DateTime.now();

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
              title: Row(
                children: [
                  Icon(
                    loan == null ? Icons.add : Icons.edit,
                    color: Colors.blue,
                  ),
                  const SizedBox(width: 8),
                  Text(loan == null ? 'ثبت وام جدید' : 'ویرایش وام'),
                ],
              ),
              content: SingleChildScrollView(
                child: Form(
                  key: formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Loan name field
                      TextFormField(
                        controller: nameController,
                        decoration: const InputDecoration(
                          labelText: 'نام وام',
                          prefixIcon: Icon(Icons.title),
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'لطفاً نام وام را وارد کنید';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Type dropdown
                      DropdownButtonFormField<String>(
                        value: type,
                        decoration: const InputDecoration(
                          labelText: 'نوع وام',
                          prefixIcon: Icon(Icons.category),
                          border: OutlineInputBorder(),
                        ),
                        items: [
                          const DropdownMenuItem(
                            value: 'بدهی',
                            child: Row(
                              children: [
                                Icon(Icons.money_off, color: Colors.red),
                                SizedBox(width: 8),
                                Text('بدهی'),
                              ],
                            ),
                          ),
                          const DropdownMenuItem(
                            value: 'طلب',
                            child: Row(
                              children: [
                                Icon(Icons.account_balance_wallet, color: Colors.green),
                                SizedBox(width: 8),
                                Text('طلب'),
                              ],
                            ),
                          ),
                        ],
                        onChanged: (newValue) {
                          setState(() {
                            type = newValue!;
                          });
                        },
                      ),
                      const SizedBox(height: 16),

                      // Person field
                      TextFormField(
                        controller: personController,
                        decoration: const InputDecoration(
                          labelText: 'نام شخص/موسسه',
                          prefixIcon: Icon(Icons.person),
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'لطفاً نام شخص یا موسسه را وارد کنید';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Amount field
                      TextFormField(
                        controller: amountController,
                        decoration: const InputDecoration(
                          labelText: 'مبلغ (افغانی)',
                          prefixIcon: Icon(Icons.attach_money),
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'لطفاً مبلغ را وارد کنید';
                          }
                          if (double.tryParse(value) == null || double.parse(value) <= 0) {
                            return 'لطفاً مبلغ معتبر وارد کنید';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Date picker
                      Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: ListTile(
                          leading: const Icon(Icons.calendar_today),
                          title: const Text('تاریخ شروع'),
                          subtitle: Text(DateFormatter.formatPersianDate(startDate)),
                          onTap: () async {
                            final selectedDate = await ShamsiDatePicker.showShamsiDatePicker(
                              context: context,
                              initialDate: startDate,
                              firstDate: DateTime(1380, 1, 1),
                              lastDate: DateTime.now(),
                              helpText: 'انتخاب تاریخ شروع',
                              confirmText: 'تأیید',
                              cancelText: 'لغو',
                            );
                            if (selectedDate != null) {
                              setState(() {
                                startDate = selectedDate;
                              });
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('لغو'),
                ),
                ElevatedButton(
                  onPressed: () {
                    if (formKey.currentState!.validate()) {
                      if (loan == null) {
                        loanService.addLoan(
                          nameController.text.trim(),
                          type,
                          personController.text.trim(),
                          double.parse(amountController.text),
                          startDate,
                        );
                      } else {
                        loanService.updateLoan(
                          loan,
                          nameController.text.trim(),
                          type,
                          personController.text.trim(),
                          double.parse(amountController.text),
                          startDate,
                        );
                      }
                      Navigator.of(context).pop();

                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(loan == null ? 'وام جدید با موفقیت ثبت شد' : 'وام با موفقیت ویرایش شد'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    }
                  },
                  child: Text(loan == null ? 'ثبت' : 'ویرایش'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _showDeleteLoanDialog(BuildContext context, Loan loan) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: const Text('حذف وام'),
          content: Text('آیا مطمئن هستید که می‌خواهید وام "${loan.name}" را حذف کنید؟'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('لغو'),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              onPressed: () {
                Provider.of<LoanService>(context, listen: false).deleteLoan(loan);
                Navigator.of(context).pop();

                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('وام با موفقیت حذف شد'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
              child: const Text('حذف'),
            ),
          ],
        );
      },
    );
  }
}
