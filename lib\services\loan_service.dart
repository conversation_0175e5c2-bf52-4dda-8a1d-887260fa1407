import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:my_fincance_app/models/loan.dart';
import 'package:my_fincance_app/models/transaction.dart';
import 'package:uuid/uuid.dart';

class LoanService extends ChangeNotifier {
  final Box<Loan> _loanBox = Hive.box<Loan>('loans');
  final Box<Transaction> _transactionBox = Hive.box<Transaction>('transactions');
  final String _userId;
  final _uuid = Uuid();

  List<Loan> _loans = [];

  LoanService(this._userId) {
    _loadLoans();
  }

  List<Loan> get loans => _loans;
  List<Loan> get debtLoans => _loans.where((l) => l.type == 'بدهی').toList();
  List<Loan> get creditLoans => _loans.where((l) => l.type == 'طلب').toList();
  List<Loan> get activeLoans => _loans.where((l) => l.status == 'فعال').toList();

  void _loadLoans() {
    _loans = _loanBox.values.where((l) => l.userId == _userId).toList();
    _updateLoanStatuses();
    notifyListeners();
  }

  /// Calculate remaining balance for a specific loan
  double getRemainingBalance(String loanId) {
    final loanTransactions = _transactionBox.values
        .where((t) => t.userId == _userId && t.loanId == loanId)
        .toList();

    final totalPaid = loanTransactions.fold<double>(0.0, (sum, transaction) => sum + transaction.amount);
    final loan = _loans.firstWhere((l) => l.id == loanId);
    return loan.initialAmount - totalPaid;
  }

  /// Get total active debt amount
  double getTotalActiveDebt() {
    return debtLoans
        .where((loan) => loan.status == 'فعال')
        .fold<double>(0.0, (sum, loan) => sum + getRemainingBalance(loan.id));
  }

  /// Get total active credit amount
  double getTotalActiveCredit() {
    return creditLoans
        .where((loan) => loan.status == 'فعال')
        .fold<double>(0.0, (sum, loan) => sum + getRemainingBalance(loan.id));
  }

  /// Update loan statuses based on remaining balances
  void _updateLoanStatuses() {
    bool hasUpdates = false;
    for (final loan in _loans) {
      if (loan.status == 'فعال') {
        final remainingBalance = getRemainingBalance(loan.id);
        if (remainingBalance <= 0) {
          loan.status = 'تمام شده';
          loan.save();
          hasUpdates = true;
        }
      }
    }
    if (hasUpdates) {
      _loadLoans();
    }
  }

  /// Get loan repayment progress (0.0 to 1.0)
  double getLoanProgress(String loanId) {
    final loan = _loans.firstWhere((l) => l.id == loanId);
    final remainingBalance = getRemainingBalance(loanId);
    final paidAmount = loan.initialAmount - remainingBalance;
    return loan.initialAmount > 0 ? (paidAmount / loan.initialAmount).clamp(0.0, 1.0) : 0.0;
  }

  void addLoan(String name, String type, String person, double initialAmount, DateTime startDate) {
    final newLoan = Loan()
      ..id = _uuid.v4()
      ..name = name
      ..type = type
      ..person = person
      ..initialAmount = initialAmount
      ..startDate = startDate
      ..status = 'فعال'
      ..userId = _userId;
    _loanBox.put(newLoan.id, newLoan);
    _loadLoans();
  }

  /// Force update loan statuses (call this after adding transactions)
  void updateLoanStatuses() {
    _updateLoanStatuses();
    notifyListeners();
  }

  void updateLoan(Loan loan, String newName, String newType, String newPerson, double newInitialAmount, DateTime newStartDate) {
    loan.name = newName;
    loan.type = newType;
    loan.person = newPerson;
    loan.initialAmount = newInitialAmount;
    loan.startDate = newStartDate;
    loan.save();
    _loadLoans();
  }

  void deleteLoan(Loan loan) {
    loan.delete();
    _loadLoans();
  }
}
