import 'package:intl/intl.dart';
import 'package:shamsi_date/shamsi_date.dart';

class DateFormatter {
  static final DateFormat _persianDateFormat = DateFormat('yyyy/MM/dd', 'fa');
  static final DateFormat _persianDateTimeFormat = DateFormat('yyyy/MM/dd HH:mm', 'fa');

  /// Format date in Solar Hijri (Persian) calendar
  static String formatPersianDate(DateTime date) {
    final jalali = Jalali.fromDateTime(date);
    return '${jalali.year}/${jalali.month.toString().padLeft(2, '0')}/${jalali.day.toString().padLeft(2, '0')}';
  }

  /// Format date and time in Solar Hijri (Persian) calendar
  static String formatPersianDateTime(DateTime date) {
    final jalali = Jalali.fromDateTime(date);
    final time = DateFormat('HH:mm').format(date);
    return '${jalali.year}/${jalali.month.toString().padLeft(2, '0')}/${jalali.day.toString().padLeft(2, '0')} $time';
  }

  /// Get Solar Hijri month name in Dari/Persian
  static String getPersianMonthName(int month) {
    const monthNames = [
      'حمل', 'ثور', 'جوزا', 'سرطان', 'اسد', 'سنبله',
      'میزان', 'عقرب', 'قوس', 'جدی', 'دلو', 'حوت'
    ];
    return monthNames[month - 1];
  }

  /// Get Solar Hijri month name from Gregorian month
  static String getSolarHijriMonthName(DateTime date) {
    final jalali = Jalali.fromDateTime(date);
    return getPersianMonthName(jalali.month);
  }

  /// Get current month and year in Solar Hijri
  static String getCurrentMonthYear() {
    final now = DateTime.now();
    final jalali = Jalali.fromDateTime(now);
    return '${getPersianMonthName(jalali.month)} ${jalali.year}';
  }

  /// Get Solar Hijri date with month name
  static String formatPersianDateWithMonthName(DateTime date) {
    final jalali = Jalali.fromDateTime(date);
    return '${jalali.day} ${getPersianMonthName(jalali.month)} ${jalali.year}';
  }

  /// Format relative date (e.g., "امروز", "دیروز", "2 روز پیش")
  static String formatRelativeDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final targetDate = DateTime(date.year, date.month, date.day);
    final difference = today.difference(targetDate).inDays;

    if (difference == 0) {
      return 'امروز';
    } else if (difference == 1) {
      return 'دیروز';
    } else if (difference == -1) {
      return 'فردا';
    } else if (difference > 1) {
      return '$difference روز پیش';
    } else {
      return '${-difference} روز بعد';
    }
  }

  /// Check if two dates are in the same Solar Hijri month
  static bool isSameMonth(DateTime date1, DateTime date2) {
    final jalali1 = Jalali.fromDateTime(date1);
    final jalali2 = Jalali.fromDateTime(date2);
    return jalali1.year == jalali2.year && jalali1.month == jalali2.month;
  }

  /// Get start of Solar Hijri month
  static DateTime getStartOfMonth(DateTime date) {
    final jalali = Jalali.fromDateTime(date);
    final startOfMonth = Jalali(jalali.year, jalali.month, 1);
    return startOfMonth.toDateTime();
  }

  /// Get end of Solar Hijri month
  static DateTime getEndOfMonth(DateTime date) {
    final jalali = Jalali.fromDateTime(date);
    final daysInMonth = jalali.monthLength;
    final endOfMonth = Jalali(jalali.year, jalali.month, daysInMonth);
    return DateTime(endOfMonth.toDateTime().year, endOfMonth.toDateTime().month, endOfMonth.toDateTime().day, 23, 59, 59);
  }

  /// Convert Gregorian date to Solar Hijri
  static Jalali toJalali(DateTime date) {
    return Jalali.fromDateTime(date);
  }

  /// Convert Solar Hijri to Gregorian date
  static DateTime fromJalali(Jalali jalali) {
    return jalali.toDateTime();
  }

  /// Get Solar Hijri year
  static int getCurrentJalaliYear() {
    return Jalali.fromDateTime(DateTime.now()).year;
  }

  /// Get Solar Hijri month
  static int getCurrentJalaliMonth() {
    return Jalali.fromDateTime(DateTime.now()).month;
  }

  /// Format Solar Hijri date for display
  static String formatJalaliDate(Jalali jalali) {
    return '${jalali.year}/${jalali.month.toString().padLeft(2, '0')}/${jalali.day.toString().padLeft(2, '0')}';
  }
}
