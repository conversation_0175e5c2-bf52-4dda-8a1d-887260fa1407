import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:my_fincance_app/models/loan.dart';
import 'package:my_fincance_app/services/loan_service.dart';
import 'package:my_fincance_app/services/transaction_service.dart';
import 'package:my_fincance_app/utils/currency_formatter.dart';
import 'package:my_fincance_app/utils/date_formatter.dart';
import 'package:my_fincance_app/utils/shamsi_date_picker.dart';

class LoanDetailPage extends StatelessWidget {
  final String loanId;

  const LoanDetailPage({super.key, required this.loanId});

  @override
  Widget build(BuildContext context) {
    return Consumer2<LoanService, TransactionService>(
      builder: (context, loanService, transactionService, child) {
        final loan = loanService.loans.firstWhere((l) => l.id == loanId);
        final remainingBalance = loanService.getRemainingBalance(loanId);
        final progress = loanService.getLoanProgress(loanId);
        final loanTransactions = transactionService.getTransactionsForLoan(loanId);
        final totalPaid = loan.initialAmount - remainingBalance;

        return Scaffold(
          appBar: AppBar(
            title: Text(loan.name),
            centerTitle: true,
            backgroundColor: Colors.blue.shade50,
            elevation: 0,
            actions: [
              IconButton(
                icon: const Icon(Icons.edit),
                onPressed: () {
                  _showEditLoanDialog(context, loan, loanService);
                },
              ),
            ],
          ),
          backgroundColor: Colors.grey.shade50,
          body: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Loan summary card
                  _buildLoanSummaryCard(loan, remainingBalance, progress, totalPaid),
                  const SizedBox(height: 20),

                  // Transaction history
                  _buildTransactionHistory(loanTransactions),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildLoanSummaryCard(Loan loan, double remainingBalance, double progress, double totalPaid) {
    final isCompleted = loan.status == 'تمام شده';
    
    return Card(
      elevation: 6,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [
              loan.type == 'بدهی' ? Colors.red.withValues(alpha: 0.1) : Colors.green.withValues(alpha: 0.1),
              loan.type == 'بدهی' ? Colors.red.withValues(alpha: 0.05) : Colors.green.withValues(alpha: 0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Icon(
                    loan.type == 'بدهی' ? Icons.money_off : Icons.account_balance_wallet,
                    size: 32,
                    color: loan.type == 'بدهی' ? Colors.red : Colors.green,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          loan.name,
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          loan.person,
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: isCompleted ? Colors.green : Colors.orange,
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: Text(
                      loan.status,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Financial details
              Row(
                children: [
                  Expanded(
                    child: _buildDetailItem(
                      'مبلغ کل',
                      CurrencyFormatter.formatWithPersianDigits(loan.initialAmount),
                      Colors.blue,
                    ),
                  ),
                  Expanded(
                    child: _buildDetailItem(
                      'پرداخت شده',
                      CurrencyFormatter.formatWithPersianDigits(totalPaid),
                      Colors.green,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildDetailItem(
                      'باقیمانده',
                      CurrencyFormatter.formatWithPersianDigits(remainingBalance),
                      remainingBalance > 0 ? Colors.red : Colors.green,
                    ),
                  ),
                  Expanded(
                    child: _buildDetailItem(
                      'تاریخ شروع',
                      DateFormatter.formatPersianDate(loan.startDate),
                      Colors.grey,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Progress section
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'پیشرفت پرداخت',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${(progress * 100).toStringAsFixed(1)}%',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  LinearProgressIndicator(
                    value: progress,
                    backgroundColor: Colors.grey.shade300,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      isCompleted ? Colors.green : Colors.blue,
                    ),
                    minHeight: 12,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailItem(String label, String value, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildTransactionHistory(List<dynamic> transactions) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تاریخچه تراکنش‌ها',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            if (transactions.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(32.0),
                  child: Column(
                    children: [
                      Icon(
                        Icons.receipt_long,
                        size: 64,
                        color: Colors.grey,
                      ),
                      SizedBox(height: 16),
                      Text(
                        'هنوز تراکنشی ثبت نشده است',
                        style: TextStyle(
                          color: Colors.grey,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
              )
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: transactions.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final transaction = transactions[index];
                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor: Colors.blue.withValues(alpha: 0.1),
                      child: const Icon(
                        Icons.payment,
                        color: Colors.blue,
                      ),
                    ),
                    title: Text(transaction.description),
                    subtitle: Text(
                      DateFormatter.formatPersianDate(transaction.date),
                    ),
                    trailing: Text(
                      CurrencyFormatter.formatWithPersianDigits(transaction.amount),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  void _showEditLoanDialog(BuildContext context, Loan loan, LoanService loanService) {
    final formKey = GlobalKey<FormState>();
    final nameController = TextEditingController(text: loan.name);
    final personController = TextEditingController(text: loan.person);
    final amountController = TextEditingController(text: loan.initialAmount.toString());
    String type = loan.type;
    DateTime startDate = loan.startDate;

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('ویرایش وام'),
              content: SingleChildScrollView(
                child: Form(
                  key: formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Name field
                      TextFormField(
                        controller: nameController,
                        decoration: const InputDecoration(
                          labelText: 'نام وام',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'نام وام الزامی است';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Type dropdown
                      DropdownButtonFormField<String>(
                        value: type,
                        decoration: const InputDecoration(
                          labelText: 'نوع وام',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'بدهی', child: Text('بدهی')),
                          DropdownMenuItem(value: 'طلب', child: Text('طلب')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            type = value!;
                          });
                        },
                      ),
                      const SizedBox(height: 16),

                      // Person field
                      TextFormField(
                        controller: personController,
                        decoration: const InputDecoration(
                          labelText: 'نام شخص',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'نام شخص الزامی است';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Amount field
                      TextFormField(
                        controller: amountController,
                        decoration: const InputDecoration(
                          labelText: 'مبلغ (افغانی)',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'مبلغ الزامی است';
                          }
                          if (double.tryParse(value) == null || double.parse(value) <= 0) {
                            return 'مبلغ باید عدد مثبت باشد';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Date picker
                      Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: ListTile(
                          leading: const Icon(Icons.calendar_today),
                          title: const Text('تاریخ شروع'),
                          subtitle: Text(DateFormatter.formatPersianDate(startDate)),
                          onTap: () async {
                            final selectedDate = await ShamsiDatePicker.showShamsiDatePicker(
                              context: context,
                              initialDate: startDate,
                              firstDate: DateTime(1380, 1, 1),
                              lastDate: DateTime.now(),
                              helpText: 'انتخاب تاریخ شروع',
                              confirmText: 'تأیید',
                              cancelText: 'لغو',
                            );
                            if (selectedDate != null) {
                              setState(() {
                                startDate = selectedDate;
                              });
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('لغو'),
                ),
                ElevatedButton(
                  onPressed: () {
                    if (formKey.currentState!.validate()) {
                      loanService.updateLoan(
                        loan,
                        nameController.text.trim(),
                        type,
                        personController.text.trim(),
                        double.parse(amountController.text),
                        startDate,
                      );
                      Navigator.of(context).pop();

                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('وام با موفقیت ویرایش شد'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    }
                  },
                  child: const Text('ویرایش'),
                ),
              ],
            );
          },
        );
      },
    );
  }
}
