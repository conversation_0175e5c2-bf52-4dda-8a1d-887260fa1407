import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:my_fincance_app/models/budget.dart';
import 'package:my_fincance_app/models/transaction.dart';
import 'package:uuid/uuid.dart';

class BudgetService extends ChangeNotifier {
  final Box<Budget> _budgetBox = Hive.box<Budget>('budgets');
  final Box<Transaction> _transactionBox = Hive.box<Transaction>('transactions');
  final String _userId;
  final _uuid = Uuid();

  List<Budget> _budgets = [];

  BudgetService(this._userId) {
    _loadBudgets();
  }

  List<Budget> get budgets => _budgets;

  /// Get budgets for current month
  List<Budget> get currentMonthBudgets {
    final now = DateTime.now();
    return _budgets.where((b) =>
      b.period.year == now.year && b.period.month == now.month
    ).toList();
  }

  void _loadBudgets() {
    _budgets = _budgetBox.values.where((b) => b.userId == _userId).toList();
    notifyListeners();
  }

  /// Get actual spending for a category in a specific month
  double getActualSpending(String categoryId, DateTime period) {
    final transactions = _transactionBox.values.where((t) =>
      t.userId == _userId &&
      t.categoryId == categoryId &&
      t.type == 'مصرف' &&
      t.date.year == period.year &&
      t.date.month == period.month
    ).toList();

    return transactions.fold<double>(0.0, (sum, t) => sum + t.amount);
  }

  /// Get budget progress (0.0 to 1.0+) for a specific budget
  double getBudgetProgress(Budget budget) {
    final actualSpending = getActualSpending(budget.categoryId, budget.period);
    return budget.amount > 0 ? actualSpending / budget.amount : 0.0;
  }

  /// Check if budget is exceeded
  bool isBudgetExceeded(Budget budget) {
    return getBudgetProgress(budget) > 1.0;
  }

  /// Get budget vs actual data for current month
  Map<String, Map<String, double>> getCurrentMonthBudgetVsActual() {
    final result = <String, Map<String, double>>{};

    for (final budget in currentMonthBudgets) {
      final actualSpending = getActualSpending(budget.categoryId, budget.period);
      result[budget.categoryId] = {
        'budget': budget.amount,
        'actual': actualSpending,
        'remaining': budget.amount - actualSpending,
      };
    }

    return result;
  }

  void addBudget(DateTime period, double amount, String categoryId) {
    final newBudget = Budget()
      ..id = _uuid.v4()
      ..period = period
      ..amount = amount
      ..categoryId = categoryId
      ..userId = _userId;
    _budgetBox.put(newBudget.id, newBudget);
    _loadBudgets();
  }

  void updateBudget(Budget budget, DateTime newPeriod, double newAmount) {
    budget.period = newPeriod;
    budget.amount = newAmount;
    budget.save();
    _loadBudgets();
  }

  void deleteBudget(Budget budget) {
    budget.delete();
    _loadBudgets();
  }
}
